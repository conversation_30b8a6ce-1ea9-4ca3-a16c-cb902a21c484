<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Log;
use App\Models\Traits\BypassesPermissionScopes;
use App\Services\FCMService;

class UserActivity extends BaseModel
{
    use BypassesPermissionScopes;
    /**
     * Status constants.
     */
    const STATUS_PENDING = 0;
    const STATUS_APPROVED = 1;
    const STATUS_REJECTED = 2;
    const STATUS_COMPLETED = 3; // No approval needed
    const STATUS_FAILED = 4; // For test activities that don't meet minimum grade

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'book_id',
        'activity_id',
        'activity_date',
        'content',
        'rating',
        'media_url',
        'status',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'activity_date' => 'datetime',
            'rating' => 'integer',
            'status' => 'integer',
            'content' => 'array',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Set initial status based on activity approval requirement
        static::creating(function ($userActivity) {
            // For test activities, status will be set when results are stored
            if ($userActivity->activity && $userActivity->activity->isTestActivity()) {
                $userActivity->status = self::STATUS_PENDING;
            } elseif ($userActivity->activity->need_approval) {
                $userActivity->status = self::STATUS_PENDING;
            } else {
                $userActivity->status = self::STATUS_COMPLETED;
            }
        });

        // Create points and review record after creation
        static::created(function ($userActivity) {
            // For test activities, points are created when test is completed and passed
            if ($userActivity->activity && $userActivity->activity->isTestActivity()) {
                // Test activities handle their own point creation in storeTestResults
                return;
            }

            if ($userActivity->status === self::STATUS_COMPLETED) {
                // Create points immediately for activities that don't need approval
                $userActivity->createActivityPoints();

                // Check for task completion after completing activity
                $userActivity->checkAndCompleteUserTasks();

                // Award activity-related rewards immediately (EARN_ACTIVITY_POINTS, COMPLETE_BOOK_ACTIVITY)
                // This does NOT award reading-related rewards (pages, books, minutes) which should only
                // be awarded from UserReadingLog when all required activities are completed
                $service = app(\App\Services\RewardCalculationService::class);
                $service->checkAndAwardActivityRewards($userActivity->user_id, $userActivity->id);

                // If this is a required activity, check if we need to update book completion and award withheld rewards
                // This handles the case where activities don't need approval and go directly to STATUS_COMPLETED
                if ($userActivity->isActivityRequired()) {
                    // Check and update book completion status, which will also award withheld rewards if all required activities are complete
                    UserReadingLog::checkAndUpdateBookCompletion($userActivity->user_id, $userActivity->book_id);

// 05.10.2025 review team awarding logic again
//                $service->checkAndAwardTeamRewards($userActivity->user_id, null, $userActivity->id);

                }
            } else {
                // Create review record for activities that need approval
                $userActivity->createReviewRecord();

                // Send FCM notification to teachers for approval-required activities
                try {
                    $fcmService = app(FCMService::class);
                    $fcmService->sendActivitySubmissionNotification($userActivity);
                } catch (\Exception $e) {
                    Log::error('Failed to send FCM submission notification: ' . $e->getMessage());
                }
            }
        });
        static::updated(function ($userActivity) {
            if ($userActivity->isDirty('status') && $userActivity->status === self::STATUS_PENDING) {
                $userActivity->review()->update(['status' => UserActivityReview::STATUS_WAITING]);
            }

            // If status changed to completed/approved and this is a required activity, check for book completion and withheld rewards
            if ($userActivity->isDirty('status') &&
                in_array($userActivity->status, [self::STATUS_COMPLETED, self::STATUS_APPROVED]) &&
                $userActivity->isActivityRequired()) {
                UserReadingLog::checkAndUpdateBookCompletion($userActivity->user_id, $userActivity->book_id);
            }

            // Check for task completion when activity status changes to completed/approved
            if ($userActivity->isDirty('status') &&
                in_array($userActivity->status, [self::STATUS_COMPLETED, self::STATUS_APPROVED])) {
                $userActivity->checkAndCompleteUserTasks();
            }
        });
    }

    /**
     * Get the user who submitted this activity.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the book this activity is for.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the activity definition.
     */
    public function activity(): BelongsTo
    {
        return $this->belongsTo(Activity::class);
    }


    /**
     * Get the review record for this activity.
     */
    public function review(): HasOne
    {
        return $this->hasOne(UserActivityReview::class);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by book.
     */
    public function scopeByBook($query, $bookId)
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Scope to filter by activity.
     */
    public function scopeByActivity($query, $activityId)
    {
        return $query->where('activity_id', $activityId);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('activity_date', [$startDate, $endDate]);
    }

    /**
     * Scope to get pending activities.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope to get approved activities.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * Scope to get completed activities.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Get the status name.
     */
    public function getStatusNameAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'Pending',
            self::STATUS_APPROVED => 'Approved',
            self::STATUS_REJECTED => 'Rejected',
            self::STATUS_COMPLETED => 'Completed',
            default => 'Unknown',
        };
    }

    /**
     * Get the localized status name.
     */
    public function getLocalizedStatusNameAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => __('admin.status_pending'),
            self::STATUS_APPROVED => __('admin.status_approved'),
            self::STATUS_REJECTED => __('admin.status_rejected'),
            self::STATUS_COMPLETED => __('admin.status_completed'),
            default => __('admin.status_unknown'),
        };
    }

    /**
     * Get the display name for the user activity.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->user->name . ' - ' . $this->activity->name . ' (' . $this->book->name . ')';
    }

    /**
     * Get summary information for the user activity.
     */
    public function getSummaryAttribute(): string
    {
        return sprintf(
            '%s on %s - %s',
            $this->activity->name,
            $this->activity_date->format('M d, Y'),
            $this->localized_status_name
        );
    }

    /**
     * Create activity points for this user activity.
     */
    public function createActivityPoints()
    {
        if ($this->activity->points > 0) {
            UserPoint::createWithoutScopes([
                'point_date' => now(),
                'user_id' => $this->user_id,
                'book_id' => $this->book_id,
                'source_id' => $this->id,
                'point_type' => UserPoint::POINT_TYPE_ACTIVITY,
                'points' => $this->activity->points,
            ]);
        }
    }

    /**
     * Create review record for this user activity.
     */
    public function createReviewRecord()
    {
        UserActivityReview::createWithoutScopes([
            'user_activity_id' => $this->id,
            'review_date' => now()->toDateString(),
            'status' => UserActivityReview::STATUS_WAITING,
        ]);
    }

    /**
     * Approve this activity and create points.
     * Note: This method is called from UserActivityReview model when status changes.
     */
    public function approve()
    {
        // Only update the user activity status, don't update the review
        // as it's already being updated from the review model
        $this->update(['status' => self::STATUS_APPROVED]);

        // Create points for the approved activity
        $this->createActivityPoints();

        // Note: Withheld rewards and task completion checking will be handled
        // by the updated() event handler when status changes to STATUS_APPROVED
    }

    /**
     * Reject this activity.
     * Note: This method is called from UserActivityReview model when status changes.
     */
    public function reject()
    {
        // Only update the user activity status, don't update the review
        // as it's already being updated from the review model
        $this->update(['status' => self::STATUS_REJECTED]);
    }

    /**
     * Reset this activity to pending status for resubmission.
     * This method should be called when a rejected activity is being resubmitted.
     */
    public function resetToPending()
    {
        // Only reset to pending if the activity is currently rejected
        if ($this->status === self::STATUS_REJECTED) {
            $this->update(['status' => self::STATUS_PENDING]);
        }
    }

    /**
     * Check if this activity can be resubmitted.
     */
    public function canBeResubmitted(): bool
    {
        return $this->status === self::STATUS_REJECTED;
    }

    /**
     * Check if user has completed the book (finished reading).
     * This checks if user has finished reading the book (has end_date), not if they've completed all required activities.
     * Users should be able to start activities for books they've finished reading.
     */
    public function userHasCompletedBook(): bool
    {
        // Check if user has any reading sessions that have finished reading for this book
        $hasCompletedSession = UserBook::where('user_id', $this->user_id)
            ->where('book_id', $this->book_id)
            ->completedSessions()
            ->exists();

        // Also check reading logs for completion flag
        $hasCompletedLog = UserReadingLog::where('user_id', $this->user_id)
            ->where('book_id', $this->book_id)
            ->where('book_completed', true)
            ->exists();

        return $hasCompletedSession || $hasCompletedLog;
    }

    /**
     * Get all status options for forms.
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_PENDING => __('admin.status_pending'),
            self::STATUS_APPROVED => __('admin.status_approved'),
            self::STATUS_REJECTED => __('admin.status_rejected'),
            self::STATUS_COMPLETED => __('admin.status_completed'),
        ];
    }

    /**
     * Get the point value this activity will award when approved.
     */
    public function getActivityPointsValue(): int
    {
        // Only return points if activity is approved or completed
        if (!in_array($this->status, [self::STATUS_APPROVED, self::STATUS_COMPLETED])) {
            return 0;
        }

        return $this->activity->points ?? 0;
    }

    /**
     * Check if this activity completion will unlock any new avatars for the student.
     */
    public function canUnlockNewAvatars(): bool
    {
        // Only approved/completed activities can unlock avatars
        if (!in_array($this->status, [self::STATUS_APPROVED, self::STATUS_COMPLETED])) {
            return false;
        }

        $newlyUnlockedAvatars = $this->getNewlyUnlockedAvatars();
        return $newlyUnlockedAvatars->count() > 0;
    }

    /**
     * Return collection of avatars that would be unlocked by this activity.
     */
    public function getNewlyUnlockedAvatars()
    {
        // Get user's current activity points (which may already include this activity's points)
        $currentActivityPoints = $this->user->getActivityPoints();

        // Get activity points before this activity was completed
        $activityPointsValue = $this->getActivityPointsValue();
        $previousActivityPoints = $currentActivityPoints - $activityPointsValue;

        // Get avatars that would be unlocked with the current total but weren't unlocked before
        $newlyUnlockedAvatars = Avatar::where('required_points', '>', $previousActivityPoints)
            ->where('required_points', '<=', $currentActivityPoints)
            ->where('active', true)
            ->orderBy('required_points', 'asc')
            ->get();

        return $newlyUnlockedAvatars;
    }

    /**
     * Check if the user has any unlocked avatars after this activity.
     */
    public function hasUnlockedAvatarsForUser(): bool
    {
        // Get user's current activity points
        $currentActivityPoints = $this->user->getActivityPoints();

        // Add this activity's points if it's approved/completed
        $activityPointsValue = $this->getActivityPointsValue();
        $totalActivityPoints = $currentActivityPoints + $activityPointsValue;

        // Check if any avatars are unlocked with this total
        return Avatar::where('required_points', '<=', $totalActivityPoints)
            ->where('active', true)
            ->exists();
    }

    /**
     * Get human-readable status text.
     */
    public function getStatusText(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => __('admin.status_pending'),
            self::STATUS_APPROVED => __('admin.status_approved'),
            self::STATUS_REJECTED => __('admin.status_rejected'),
            self::STATUS_COMPLETED => __('admin.status_completed'),
            self::STATUS_FAILED => __('admin.status_failed'),
            default => __('admin.status_unknown'),
        };
    }

    /**
     * Check if this is a test activity (quiz or vocabulary test).
     */
    public function isTestActivity(): bool
    {
        return $this->activity && $this->activity->isTestActivity();
    }

    /**
     * Get the score for test activities.
     */
    public function getTestScore(): ?float
    {
        if (!$this->isTestActivity() || !$this->content) {
            return null;
        }

        return $this->content['score_percentage'] ?? null;
    }

    /**
     * Check if the user passed the test (met minimum grade requirement).
     */
    public function passedTest(): bool
    {
        if (!$this->isTestActivity()) {
            return false;
        }

        // Check status first - if it's completed, the test was passed
        if ($this->status === self::STATUS_COMPLETED) {
            return true;
        }

        // If status is failed, the test was not passed
        if ($this->status === self::STATUS_FAILED) {
            return false;
        }

        // Fallback to score checking for backward compatibility
        $score = $this->getTestScore();
        $minGrade = $this->activity->min_grade ?? 0;

        return $score !== null && $score >= $minGrade;
    }

    /**
     * Get the number of attempts for this activity by the user.
     */
    public function getAttemptCount(): int
    {
        return self::where('user_id', $this->user_id)
            ->where('book_id', $this->book_id)
            ->where('activity_id', $this->activity_id)
            ->count();
    }

    /**
     * Check if the user can retry this activity.
     */
    public function canRetry(): bool
    {
        if (!$this->activity) {
            return false;
        }

        $attemptCount = $this->getAttemptCount();
        $allowedTries = $this->activity->allowed_tries ?? 1;

        return $attemptCount < $allowedTries;
    }

    /**
     * Get remaining retry attempts.
     */
    public function getRemainingAttempts(): int
    {
        if (!$this->activity) {
            return 0;
        }

        $attemptCount = $this->getAttemptCount();
        $allowedTries = $this->activity->allowed_tries ?? 1;

        return max(0, $allowedTries - $attemptCount);
    }

    /**
     * Validation rules for the model.
     */
    public static function validationRules(): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'book_id' => [
                'required',
                'exists:books,id',
                function ($attribute, $value, $fail) {
                    $book = Book::find($value);
                    if ($book && !$book->canCreateActivities()) {
                        $fail(__('admin.cannot_create_activity_inactive_book'));
                    }
                }
            ],
            'activity_id' => ['required', 'exists:activities,id'],
            'activity_date' => ['required', 'date', 'before_or_equal:today'],
            'content' => ['nullable'],
            'rating' => ['nullable', 'integer', 'min:1', 'max:10'],
            'media_url' => ['nullable', 'file', 'mimetypes:image/jpeg,image/jpg,image/png,image/webp,image/gif,audio/mpeg,audio/mp3,audio/wav,audio/x-wav,audio/aac,audio/mp4,audio/x-m4a,audio/m4a,audio/ogg,audio/webm', 'max:10240'],
            'status' => ['integer', 'in:0,1,2,3,4'],
        ];
    }

    /**
     * Store test results in the content field.
     */
    public function storeTestResults(array $quizData, array $userAnswers, array $scoreData): void
    {
        $this->content = [
            'quiz_type' => $quizData['quiz_type'] ?? 'quiz',
            'total_questions' => $scoreData['total_questions'],
            'correct_answers' => $scoreData['correct_answers'],
            'score_percentage' => $scoreData['score_percentage'],
            'user_answers' => $userAnswers,
            'results' => $scoreData['results'],
            'completed_at' => now()->toISOString(),
        ];

        // Determine if user passed the test
        $passed = true;
        if ($this->activity && $this->activity->min_grade) {
            $passed = $scoreData['score_percentage'] >= $this->activity->min_grade;
        }

        // Set status based on test result and approval requirements
        if (!$passed) {
            // Failed the test
            $this->status = self::STATUS_FAILED;
        } elseif ($this->activity && $this->activity->need_approval) {
            // Passed but needs approval
            $this->status = self::STATUS_PENDING;
        } else {
            // Passed and no approval needed
            $this->status = self::STATUS_COMPLETED;
        }

        $this->save();

        // Handle post-completion logic based on final status
        if ($this->status === self::STATUS_COMPLETED) {
            // Create points immediately for completed activities
            $this->createActivityPoints();

            // Award activity-related rewards immediately for test completion
            // This awards EARN_ACTIVITY_POINTS and COMPLETE_BOOK_ACTIVITY rewards
            $service = app(\App\Services\RewardCalculationService::class);
            $service->checkAndAwardActivityRewards($this->user_id, $this->id);

            // If this is a required activity, check if we need to update book completion and award withheld reading points
            if ($this->isActivityRequired()) {
                UserReadingLog::checkAndUpdateBookCompletion($this->user_id, $this->book_id);
            }

            // Check for task completion after completing test activity
            $this->checkAndCompleteUserTasks();
        } elseif ($this->status === self::STATUS_PENDING && $passed) {
            // Create review record for activities that need approval
            $this->createReviewRecord();
        }
        // For STATUS_FAILED, no points or reviews are created
    }

    /**
     * Check if this activity is required with class-specific resolution.
     *
     * This method properly applies ClassActivityResolutionScope to determine
     * if the activity is required, taking into account class-specific overrides.
     *
     * @return bool True if the activity is required (with class resolution)
     */
    public function isActivityRequired(): bool
    {
        if (!$this->activity) {
            return false;
        }

        // Use class-specific resolution to get the activity with proper required status
        // This applies the ClassActivityResolutionScope which uses COALESCE to merge
        // base activity values with class-specific overrides
        $resolvedActivity = Activity::where('activities.id', $this->activity_id)->first();

        return $resolvedActivity ? $resolvedActivity->required : false;
    }

    /**
     * Get detailed test results.
     */
    public function getTestResults(): ?array
    {
        if (!$this->isTestActivity() || !$this->content) {
            return null;
        }

        return [
            'quiz_type' => $this->content['quiz_type'] ?? 'quiz',
            'total_questions' => $this->content['total_questions'] ?? 0,
            'correct_answers' => $this->content['correct_answers'] ?? 0,
            'score_percentage' => $this->content['score_percentage'] ?? 0,
            'passed' => $this->passedTest(),
            'min_grade_required' => $this->activity->min_grade ?? 0,
            'completed_at' => $this->content['completed_at'] ?? null,
            'attempt_number' => $this->getAttemptCount(),
            'remaining_attempts' => $this->getRemainingAttempts(),
        ];
    }

    /**
     * Scope to get test activities only.
     */
    public function scopeTestActivities($query)
    {
        return $query->whereHas('activity', function ($q) {
            $q->testActivities();
        });
    }

    /**
     * Scope to get passed test activities.
     */
    public function scopePassedTests($query)
    {
        return $query->testActivities()->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope to get failed test activities.
     */
    public function scopeFailedTests($query)
    {
        return $query->testActivities()->where('status', self::STATUS_FAILED);
    }

    /**
     * Check and complete any UserTask instances that should be marked as completed
     * based on the current activity completion.
     *
     * This method uses the TaskProgressCalculationService to determine if any
     * assigned tasks for this user should be marked as completed based on their
     * current progress including this activity.
     *
     * @return array Array of UserTask instances that were marked as completed
     */
    public function checkAndCompleteUserTasks(): array
    {
        $service = app(\App\Services\TaskProgressCalculationService::class);
        return $service->checkAndCompleteUserTasks($this->user_id, $this->book_id);
    }
}
