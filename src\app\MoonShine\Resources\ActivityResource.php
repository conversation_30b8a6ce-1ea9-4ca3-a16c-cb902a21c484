<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\Activity;
use App\Models\ActivityCategory;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Textarea;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Url;
use MoonShine\UI\Fields\Select;
use MoonShine\UI\Fields\Switcher;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;

#[Icon('puzzle-piece')]
class ActivityResource extends BaseResource
{
    protected string $model = Activity::class;

    protected string $column = 'name';

    protected array $with = ['category'];

    public function getTitle(): string
    {
        return __('admin.activities');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.category'),
                'category',
                formatted: fn(ActivityCategory $category) => $category->name,
                resource: ActivityCategoryResource::class
            )
                ->sortable(),

            Text::make(__('admin.name'), 'name')
                ->sortable(),

            Select::make(__('admin.activity_type'), 'activity_type')
                ->options(Activity::getActivityTypeOptions())
                ->sortable(),

            Number::make(__('admin.points'), 'points')
                ->sortable(),

            Switcher::make(__('admin.required'), 'required')
                ->sortable(),
                
            Switcher::make(__('admin.need_approval'), 'need_approval')
                ->sortable(),

            Switcher::make(__('admin.active'), 'active')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    BelongsTo::make(
                        __('admin.category'),
                        'category',
                        formatted: fn(ActivityCategory $category) => $category->name,
                        resource: ActivityCategoryResource::class
                    )
                        ->required()
                        ->searchable()
                        ->valuesQuery(function ($query) {
                            return $query->where('active', true);
                        })
                        ->hint(__('admin.activity_category_hint')),

                    Select::make(__('admin.activity_type'), 'activity_type')
                        ->required()
                        ->options(Activity::getActivityTypeOptions())
                        ->default(Activity::ACTIVITY_TYPE_WRITING)
                        ->hint(__('admin.activity_type_hint')),

                    Select::make(__('admin.media_type'), 'media_type')
                        ->options(Activity::getMediaTypeOptions())
                        ->nullable()
                        ->hint(__('admin.media_type_hint')),
                ]),

                Text::make(__('admin.name'), 'name')
                    ->required(),

                Textarea::make(__('admin.description'), 'description')
                    ->hint(__('admin.activity_description_hint')),

                Flex::make([
                    Number::make(__('admin.min_word_count'), 'min_word_count')
                        ->min(1)
                        ->hint(__('admin.min_word_count_hint')),

                    Number::make(__('admin.min_rating'), 'min_rating')
                        ->min(1)
                        ->hint(__('admin.min_rating_hint')),

                    Number::make(__('admin.max_rating'), 'max_rating')
                        ->min(1)
                        ->hint(__('admin.max_rating_hint')),
                ]),

                Url::make(__('admin.media_url'), 'media_url')
                    ->hint(__('admin.media_url_hint')),

                // Test Activity Configuration
                Flex::make([
                    Number::make(__('admin.question_count'), 'question_count')
                        ->min(1)
                        ->max(10)
                        ->hint(__('admin.question_count_hint')),

                    Number::make(__('admin.choices_count'), 'choices_count')
                        ->min(1)
                        ->max(6)
                        ->hint(__('admin.choices_count_hint')),

                    Number::make(__('admin.min_grade'), 'min_grade')
                        ->min(0)
                        ->max(100)
                        ->hint(__('admin.min_grade_hint')),
                ]),

                Flex::make([
                    Number::make(__('admin.allowed_tries'), 'allowed_tries')
                        ->min(1)
                        ->default(1)
                        ->hint(__('admin.allowed_tries_hint')),

                    Switcher::make(__('admin.required'), 'required')
                        ->default(false)
                        ->hint(__('admin.required_activity_hint')),
                ]),

                Flex::make([
                    Number::make(__('admin.points'), 'points')
                        ->required()
                        ->min(0)
                        ->default(10),

                    Switcher::make(__('admin.need_approval'), 'need_approval')
                        ->default(false),

                    Switcher::make(__('admin.active'), 'active')
                        ->default(true),
                ]),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.category'),
                'category',
                formatted: fn(ActivityCategory $category) => $category->name,
                resource: ActivityCategoryResource::class
            ),

            Text::make(__('admin.name'), 'name'),
            Text::make(__('admin.activity_type'), 'localized_activity_type_name'),
            Textarea::make(__('admin.description'), 'description'),
            Number::make(__('admin.question_count'), 'question_count'),
            Number::make(__('admin.choices_count'), 'choices_count'),
            Number::make(__('admin.min_grade'), 'min_grade'),
            Number::make(__('admin.allowed_tries'), 'allowed_tries'),
            Switcher::make(__('admin.required'), 'required'),
            Number::make(__('admin.min_word_count'), 'min_word_count'),
            Number::make(__('admin.min_rating'), 'min_rating'),
            Number::make(__('admin.max_rating'), 'max_rating'),
            Url::make(__('admin.media_url'), 'media_url'),
            Number::make(__('admin.points'), 'points'),
            Switcher::make(__('admin.need_approval'), 'need_approval'),
            Switcher::make(__('admin.active'), 'active'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'category_id' => ['required', 'exists:activity_categories,id'],
            'activity_type' => ['required', 'integer', 'in:1,2,3,4,5,6,7'],
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'question_count' => ['nullable', 'integer', 'min:1', 'max:10'],
            'choices_count' => ['nullable', 'integer', 'min:1', 'max:6'],
            'min_grade' => ['nullable', 'integer', 'min:0', 'max:100'],
            'required' => ['boolean'],
            'allowed_tries' => ['integer', 'min:1'],
            'min_word_count' => ['nullable', 'integer', 'min:1'],
            'min_rating' => ['nullable', 'integer', 'min:1'],
            'max_rating' => ['nullable', 'integer', 'min:1', 'gte:min_rating'],
            'media_url' => ['nullable', 'file', 'mimetypes:image/jpeg,image/jpg,image/png,image/webp,image/gif,audio/mpeg,audio/mp3,audio/wav,audio/x-wav,audio/aac,audio/mp4,audio/x-m4a,audio/m4a,audio/ogg,audio/webm', 'max:10240'],
            'points' => ['required', 'integer', 'min:0'],
            'need_approval' => ['boolean'],
            'active' => ['boolean'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['name', 'description', 'category.name'];
    }

    protected function getDefaultSort(): array
    {
        return ['category.name' => 'asc', 'name' => 'asc'];
    }
}
