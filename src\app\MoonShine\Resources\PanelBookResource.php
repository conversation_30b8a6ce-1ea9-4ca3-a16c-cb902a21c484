<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use MoonShine\Contracts\UI\ComponentContract;
use MoonShine\Laravel\Http\Responses\MoonShineJsonResponse;
use MoonShine\Laravel\MoonShineRequest;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;

use App\Models\{Book, BookType, User};
use MoonShine\Laravel\Fields\Relationships\{BelongsTo, BelongsToMany, HasMany};
use MoonShine\Support\{Attributes\Icon, Enums\JsEvent, Enums\ToastType, AlpineJs, ListOf};
use MoonShine\UI\Components\{Layout\Box, Layout\Flex, ActionButton, Badge, CardsBuilder};
use MoonShine\UI\Fields\{Field, Image, Number, Preview, Switcher, Text};

#[Icon('book-open')]

class PanelBookResource extends BookResource
{
    use WithRolePermissions;

    protected function indexFields(): iterable
    {
        return [
        ];
    }
/*
    protected function formFields(): iterable
    {
        return [
            Box::make([
                Flex::make([
                    Text::make(__('admin.isbn'), 'isbn')
                        ->required()
                        ->placeholder(__('admin.enter_isbn'))
                        ->hint(__('admin.isbn_hint'))
                        ->unless(fn() => $this->isCreateFormPage(), fn(Field $field) => $field->readonly()),

                        
                    ActionButton::make(__('admin.search_import_book'))
                            ->method('searchAndImportBook') 
                            ->withSelectorsParams(['isbn' => 'input[name="isbn"]'])
                            ->secondary()
                            ->icon('magnifying-glass')
                            ->canSee(fn () => $this->isCreateFormPage()),
                ]),

                Text::make(__('admin.name'), 'name')
                    ->required()
                    ->readOnly(),

                Flex::make([
                    Text::make(__('admin.publisher'), 'publisher.name')
                        ->readOnly(),

                    BelongsTo::make(
                        __('admin.book_type'),
                        'bookType',
                        formatted: fn(BookType $bookType) => $bookType->name,
                        resource: BookTypeResource::class)
                        ->withImage('thumbnail')
                        ->required()
                        ->placeholder(__('admin.select_book_type')),
                ]),

                Flex::make([
                    Number::make(__('admin.page_count'), 'page_count')
                        ->required()
                        ->min(1)
                        ->placeholder(__('admin.enter_page_count')),

                    Number::make(__('admin.year_of_publish'), 'year_of_publish')
                        ->required()
                        ->readOnly(),
                ]),
                Text::make(__('admin.author_names'), 'author_names')
                    ->readOnly(),
                BelongsToMany::make(__('admin.categories'), 'categories', resource: CategoryResource::class, formatted: 'name')
                    ->selectMode(),
                Preview::make(__('admin.cover_image'), formatted:fn($book) => asset('storage/' . $book['cover_image']))
                    ->image(),
                Switcher::make(__('admin.active'), 'active')
                    ->default(true)
                    ->disabled(),
            ]),


        ];
    }
*/
    protected function detailFields(): iterable
    {
        return [
            Image::make(__('admin.cover_image'), 'cover_image'),
            Text::make(__('admin.name'), 'name')->unescape(),
            Text::make(__('admin.isbn'), 'isbn'),
            Text::make(__('admin.publisher'), 'publisher.name'),
            Text::make(__('admin.book_type'), 'bookType.name'),
            Number::make(__('admin.page_count'), 'page_count'),
            Number::make(__('admin.year_of_publish'), 'year_of_publish'),
            BelongsToMany::make(__('admin.authors'), 'authors', resource: AuthorResource::class, formatted: 'name')
                ->inLine(separator: ' ', badge: fn($model, $value) => Badge::make((string) $value, 'primary')),
            BelongsToMany::make(__('admin.categories'), 'categories', resource: CategoryResource::class, formatted: 'name')
                ->inLine(separator: ' ', badge: fn($model, $value) => Badge::make((string) $value, 'primary')),
            Switcher::make(__('admin.active'), 'active'),
            ...parent::getCommonDetailFields(),

            HasMany::make(__('admin.book_questions'), 'questions', resource: PanelBookQuestionResource::class)
                ->creatable()
                ->searchable(false),
            HasMany::make(__('admin.book_words'), 'words', resource: PanelBookWordResource::class)
                ->creatable()
                ->searchable(false),
        ];
    }

    protected function detailButtons(): ListOf
    {
        //if book is in class books, show remove button, else show add button
        if($this->item->classBooks()->exists()) {
            $button = ActionButton::make(__('admin.remove_from_class_books'))
                ->method('removeFromClassBooks') 
                ->error()
                ->withConfirm(__('admin.confirm_remove_from_class_books'))
                ->icon('x-mark');
        } else {
            $button = ActionButton::make(__('admin.add_to_class_books'))
                ->method('addToClassBooks') 
                ->success()
                ->withConfirm(__('admin.confirm_add_to_class_books'))
                ->icon('check');
        }        
        return parent::detailButtons()->prepend(
            $button
        );        
    }
   
    public function addOrRemoveFromClassBooks(MoonShineRequest $request, $action): MoonShineJsonResponse
    {   
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User) || (!$user->isTeacher() && !$user->isSystemAdmin())) {
            return MoonShineJsonResponse::make()->toast(__('admin.unauthorized_action'), ToastType::ERROR);
        }

        $id = $request->get('resourceItem');
        if (!$id) {
            return MoonShineJsonResponse::make()->toast(__('admin.invalid_request'), ToastType::ERROR);
        }
        $book = Book::where('id', $id)->first();
        if (!$book) {
            return MoonShineJsonResponse::make()->toast(__('admin.book_not_found'), ToastType::ERROR);
        }

        $successMessage = ($action == 'add') ? __('admin.book_added_to_class_books_successfully') : __('admin.book_removed_from_class_books_successfully');
        $errorMessage = ($action == 'add') ? __('admin.book_added_to_class_books_error') : __('admin.book_removed_from_class_books_error');

        $result = ($action == 'add') ? $book->addClassBook($user->id) : $book->removeClassBook($user->id);

        $message = ($result > 0) ? $successMessage : $errorMessage;
        $toastType = ($result > 0) ? ToastType::SUCCESS :  ToastType::ERROR;
        return MoonShineJsonResponse::make()
        ->toast($message, $toastType)
        ->redirect($this->getDetailPageUrl($book->id));
    }


    public function addToClassBooks(MoonShineRequest $request): MoonShineJsonResponse
    {   
        return $this->addOrRemoveFromClassBooks($request, 'add');
    }

    public function removeFromClassBooks(MoonShineRequest $request): MoonShineJsonResponse
    {   
        return $this->addOrRemoveFromClassBooks($request, 'remove');
    }   

}
