<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Book;
use App\Models\Activity;
use App\Models\UserActivity;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Livewire\Livewire;

class AudioRecordingUploadTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $book;
    protected $audioActivity;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create();

        // Create test book manually
        $this->book = new Book();
        $this->book->name = 'Test Audio Book';
        $this->book->isbn = '9781234567890';
        $this->book->page_count = 100;
        $this->book->year_of_publish = 2023;
        $this->book->created_by = $this->user->id;
        $this->book->save();

        // Create audio media activity manually
        $this->audioActivity = new Activity();
        $this->audioActivity->activity_type = Activity::ACTIVITY_TYPE_MEDIA;
        $this->audioActivity->media_type = Activity::MEDIA_TYPE_AUDIO;
        $this->audioActivity->name = 'Audio Recording Test';
        $this->audioActivity->description = 'Test audio recording activity';
        $this->audioActivity->points = 10;
        $this->audioActivity->active = true;
        $this->audioActivity->save();

        Storage::fake('public');
    }

    /** @test */
    public function audio_activity_component_loads_correctly()
    {
        $this->actingAs($this->user);

        $component = Livewire::test('mobile.upload-activity', [
            'book' => $this->book->id,
            'activity' => $this->audioActivity->id
        ]);

        $component->assertSet('book.id', $this->book->id)
                  ->assertSet('activity.id', $this->audioActivity->id)
                  ->assertSet('activity.media_type', Activity::MEDIA_TYPE_AUDIO)
                  ->assertSet('mode', 'create');
    }

    /** @test */
    public function webm_audio_file_can_be_uploaded()
    {
        $this->actingAs($this->user);

        // Create a fake WebM audio file
        $webmFile = UploadedFile::fake()->create('recording.webm', 1024, 'audio/webm');

        $component = Livewire::test('mobile.upload-activity', [
            'book' => $this->book->id,
            'activity' => $this->audioActivity->id
        ]);

        $component->set('mediaFile', $webmFile)
                  ->set('description', 'Test WebM audio recording')
                  ->call('submitActivity');

        // Check that the activity was created
        $this->assertDatabaseHas('user_activities', [
            'user_id' => $this->user->id,
            'book_id' => $this->book->id,
            'activity_id' => $this->audioActivity->id,
            'content' => 'Test WebM audio recording'
        ]);

        // Check that file was stored
        $userActivity = UserActivity::where('user_id', $this->user->id)
            ->where('activity_id', $this->audioActivity->id)
            ->first();

        $this->assertNotNull($userActivity->media_url);
        $this->assertTrue(Storage::disk('public')->exists($userActivity->media_url));
    }

    /** @test */
    public function mp3_audio_file_can_be_uploaded()
    {
        $this->actingAs($this->user);

        // Create a fake MP3 audio file
        $mp3File = UploadedFile::fake()->create('recording.mp3', 2048, 'audio/mpeg');

        $component = Livewire::test('mobile.upload-activity', [
            'book' => $this->book->id,
            'activity' => $this->audioActivity->id
        ]);

        $component->set('mediaFile', $mp3File)
                  ->set('description', 'Test MP3 audio recording')
                  ->call('submitActivity');

        // Check that the activity was created
        $this->assertDatabaseHas('user_activities', [
            'user_id' => $this->user->id,
            'book_id' => $this->book->id,
            'activity_id' => $this->audioActivity->id,
            'content' => 'Test MP3 audio recording'
        ]);
    }

    /** @test */
    public function invalid_file_type_is_rejected()
    {
        $this->actingAs($this->user);

        // Create a fake text file (invalid for audio activity)
        $textFile = UploadedFile::fake()->create('document.txt', 1024, 'text/plain');

        $component = Livewire::test('mobile.upload-activity', [
            'book' => $this->book->id,
            'activity' => $this->audioActivity->id
        ]);

        $component->set('mediaFile', $textFile)
                  ->set('description', 'Test invalid file')
                  ->call('submitActivity')
                  ->assertHasErrors('mediaFile');

        // Check that no activity was created
        $this->assertDatabaseMissing('user_activities', [
            'user_id' => $this->user->id,
            'book_id' => $this->book->id,
            'activity_id' => $this->audioActivity->id
        ]);
    }

    /** @test */
    public function large_audio_file_is_rejected()
    {
        $this->actingAs($this->user);

        // Create a fake audio file that's too large (15MB > 10MB limit)
        $largeFile = UploadedFile::fake()->create('large-recording.mp3', 15360, 'audio/mpeg');

        $component = Livewire::test('mobile.upload-activity', [
            'book' => $this->book->id,
            'activity' => $this->audioActivity->id
        ]);

        $component->set('mediaFile', $largeFile)
                  ->set('description', 'Test large file')
                  ->call('submitActivity')
                  ->assertHasErrors('mediaFile');

        // Check that no activity was created
        $this->assertDatabaseMissing('user_activities', [
            'user_id' => $this->user->id,
            'book_id' => $this->book->id,
            'activity_id' => $this->audioActivity->id
        ]);
    }

    /** @test */
    public function audio_activity_requires_media_file()
    {
        $this->actingAs($this->user);

        $component = Livewire::test('mobile.upload-activity', [
            'book' => $this->book->id,
            'activity' => $this->audioActivity->id
        ]);

        $component->set('description', 'Test without file')
                  ->call('submitActivity')
                  ->assertHasErrors('mediaFile');

        // Check that no activity was created
        $this->assertDatabaseMissing('user_activities', [
            'user_id' => $this->user->id,
            'book_id' => $this->book->id,
            'activity_id' => $this->audioActivity->id
        ]);
    }

    /** @test */
    public function non_audio_activity_is_rejected()
    {
        $this->actingAs($this->user);

        // Create an image activity instead of audio
        $imageActivity = Activity::factory()->create([
            'activity_type' => Activity::ACTIVITY_TYPE_MEDIA,
            'media_type' => Activity::MEDIA_TYPE_IMAGE,
            'name' => 'Image Upload Test',
            'active' => true
        ]);

        // Try to access with audio activity component - should fail
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        Livewire::test('mobile.upload-activity', [
            'book' => $this->book->id,
            'activity' => $imageActivity->id
        ]);
    }

    /** @test */
    public function component_shows_recording_interface_for_audio_activities()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('mobile.books.activities.upload', [
            'book' => $this->book->id,
            'activity' => $this->audioActivity->id
        ]));

        $response->assertStatus(200)
                 ->assertSee('x-data="audioRecorder"')
                 ->assertSee('Record Audio')
                 ->assertSee('toggleRecording')
                 ->assertSee('togglePlayback')
                 ->assertSee('deleteRecording');
    }
}
