<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\ClassBook;
use App\Models\SchoolClass;
use App\Models\Book;
use App\Models\User;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;

#[Icon('book-open')]
class ClassBookResource extends BaseResource
{
    protected string $model = ClassBook::class;

    protected string $column = 'display_name';

    protected array $with = ['schoolClass.school', 'book'];

    public function getTitle(): string
    {
        return __('admin.class_books');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.class'),
                'schoolClass',
                formatted: fn(SchoolClass $class) => $class->name . ' (' . $class->school->name . ')',
                resource: SchoolClassResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: BookResource::class
            )
                ->sortable(),

            Text::make(__('admin.isbn'), 'book.isbn')
                ->sortable(),

            Text::make(__('admin.publisher'), 'book.publisher.name')
                ->sortable(),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(
                    __('admin.class'),
                    'schoolClass',
                    formatted: fn(SchoolClass $class) => $class->name . ' (' . $class->school->name . ')',
                    resource: SchoolClassResource::class
                )
                    ->required()
                    ->searchable(),

                BelongsTo::make(
                    __('admin.book'),
                    'book',
                    formatted: fn(Book $book) => html_entity_decode($book->name) . ' - ' . $book->isbn,
                    resource: BookResource::class
                )
                    ->required()
                    ->searchable(),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.class'),
                'schoolClass',
                formatted: fn(SchoolClass $class) => $class->name . ' (' . $class->school->name . ')',
                resource: SchoolClassResource::class
            ),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: BookResource::class
            ),

            Text::make(__('admin.isbn'), 'book.isbn'),
            Text::make(__('admin.publisher'), 'book.publisher.name'),
            Text::make(__('admin.book_type'), 'book.bookType.name'),
            Text::make(__('admin.page_count'), 'book.page_count'),
            Text::make(__('admin.year_of_publish'), 'book.year_of_publish'),

            Text::make(__('admin.summary'), 'summary'),
            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        return [
            'class_id' => ['required', 'exists:school_classes,id'],
            'book_id' => ['required', 'exists:books,id'],
            ...parent::getCommonRules($item),
        ];
    }

    protected function search(): array
    {
        return ['schoolClass.name', 'schoolClass.school.name', 'book.name', 'book.isbn'];
    }

    protected function getDefaultSort(): array
    {
        return ['schoolClass.school.name' => 'asc', 'schoolClass.name' => 'asc', 'book.name' => 'asc'];
    }

    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return $builder->where('id', 0); // No access if not authenticated
        }

        // System Admin can see all class book assignments
        if ($user->isSystemAdmin()) {
            return $builder;
        }

        // School Admin can see assignments in their schools
        if ($user->isSchoolAdmin()) {
            $userSchoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();

            if (empty($userSchoolIds)) {
                return $builder->where('id', 0);
            }

            return $builder->whereHas('schoolClass', function ($q) use ($userSchoolIds) {
                $q->whereIn('school_id', $userSchoolIds);
            });
        }

        // Teachers can only see books for their default class
        if ($user->isTeacher()) {
            $defaultClass = $user->getDefaultClass();
            if ($defaultClass) {
                return $builder->where('class_id', $defaultClass->class_id);
            }
        }

        // Students have no access to class book management
        return $builder->where('id', 0);
    }
}
