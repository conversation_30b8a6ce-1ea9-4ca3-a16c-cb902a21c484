<?php

namespace App\Services\BookDiscovery\Providers;

use App\Services\BookDiscovery\AbstractBookDiscoveryProvider;

class DRBookProvider extends AbstractBookDiscoveryProvider
{
    /**
     * D&R specific parsing logic.
     */
    protected function parseBookData(string $html): ?array
    {
        // First try the new D&R structure with product properties
        $bookData = $this->parseDRProductProperties($html);

        // If that fails, try the parent's generic parsing
        if (!$bookData) {
            $bookData = parent::parseBookData($html);
        }

        if (!$bookData) {
            return null;
        }

        // D&R specific post-processing
        $bookData = $this->postProcessDRData($bookData);

        return $bookData;
    }

    /**
     * Parse D&R's product properties structure.
     */
    protected function parseDRProductProperties(string $html): ?array
    {
        $dom = new \DOMDocument();
        @$dom->loadHTML('<?xml encoding="UTF-8">' . $html);
        $xpath = new \DOMXPath($dom);

        $bookData = [];

        // Look for the product properties container
        $propertyContainers = $xpath->query('//div[contains(@class, "product-property")]//ul[contains(@class, "js-list-prd-property")]');

        if ($propertyContainers->length === 0) {
            // Try alternative container structure
            $propertyContainers = $xpath->query('//ul[contains(@class, "js-list-prd-property")]');
        }

        if ($propertyContainers->length === 0) {
            return null;
        }

        $container = $propertyContainers->item(0);
        $listItems = $xpath->query('.//li', $container);

        foreach ($listItems as $li) {
            $strongElements = $xpath->query('.//strong', $li);
            $spanElements = $xpath->query('.//span', $li);

            if ($strongElements->length > 0 && $spanElements->length > 0) {
                $label = trim($strongElements->item(0)->textContent);
                $value = trim($spanElements->item(0)->textContent);

                if (empty($value)) {
                    continue;
                }

                // Map D&R labels to our book data structure
                switch ($label) {
                    case 'Kitap Adı:':
                        $bookData['name'] = html_entity_decode($value);
                        break;
                    case 'Yazar:':
                        $bookData['author'] = [$value];
                        break;
                    case 'Yayınevi:':
                        // split on ' - ' text and take the first part
                        $parts = explode(' - ', $value);
                        $value = $parts[0];                                            
                        $bookData['publisher'] = $value;
                        break;
                    case 'İlk Baskı Yılı:':
                    case 'Baskı Yılı:':
                        if (preg_match('/(\d{4})/', $value, $matches)) {
                            $bookData['year'] = (int) $matches[1];
                        }
                        break;
                    case 'Sayfa Sayısı:':
                        if (preg_match('/(\d+)/', $value, $matches)) {
                            $bookData['page_count'] = (int) $matches[1];
                        }
                        break;
                    case 'Barkod:':
                        $bookData['isbn'] = $value;
                        break;
                }
            }
        }

        // If page count wasn't found in structured format, try alternative HTML structure
        if (!isset($bookData['page_count'])) {
            $this->extractPageCountFromDetailDiv($xpath, $bookData);
        }

        // Look for the cover image
        $imageContainers = $xpath->query('//img[contains(@class, "js-prd-first-image")]');
        if ($imageContainers->length > 0) {
            $imageElement = $imageContainers->item(0);
            if ($imageElement instanceof \DOMElement) {
                $imageUrl = $imageElement->getAttribute('src');
                if (!empty($imageUrl)) {
                    $bookData['cover_image'] = $imageUrl;
                }
            }
        }

        // Validate that we have at least a title
        if (empty($bookData['name'])) {
            return null;
        }

        // Add source information
        $bookData['source'] = $this->getName();

        return $bookData;
    }

    /**
     * Post-process D&R specific data.
     */
    protected function postProcessDRData(array $data): array
    {
        // Clean up author names (D&R sometimes includes extra text)
        if (isset($data['author'])) {
            if (is_array($data['author'])) {
                $data['author'] = array_map(function($author) {
                    return $this->cleanAuthorName($author);
                }, $data['author']);
            } else {
                $data['author'] = [$this->cleanAuthorName($data['author'])];
            }
        }

        // Clean up publisher name
        if (isset($data['publisher'])) {
            $data['publisher'] = $this->cleanPublisherName($data['publisher']);
        }

        // Extract page count from various formats
        if (isset($data['page_count'])) {
            $data['page_count'] = $this->extractPageCount($data['page_count']);
        }

        // Clean up category names
        if (isset($data['category'])) {
            if (is_array($data['category'])) {
                $data['category'] = array_map('trim', $data['category']);
                $data['category'] = array_filter($data['category']);
            }
        }

        return $data;
    }

    /**
     * Clean author name from D&R format.
     */
    protected function cleanAuthorName(string $author): string
    {
        // Remove common prefixes/suffixes
        $author = preg_replace('/^(Yazar:|Author:)\s*/i', '', $author);
        $author = preg_replace('/\s*\(.*?\)$/', '', $author); // Remove parenthetical info
        
        return trim($author);
    }

    /**
     * Clean publisher name from D&R format.
     */
    protected function cleanPublisherName(string $publisher): string
    {
        // Remove common prefixes
        $publisher = preg_replace('/^(Yayınevi:|Publisher:)\s*/i', '', $publisher);
        
        return trim($publisher);
    }

    /**
     * Extract page count from various text formats.
     */
    protected function extractPageCount(string $text): ?int
    {
        // Try different patterns
        $patterns = [
            '/(\d+)\s*sayfa/i',
            '/(\d+)\s*pages?/i',
            '/(\d+)\s*sf/i',
            '/(\d+)/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                $count = (int) $matches[1];
                if ($count > 0 && $count <= 10000) {
                    return $count;
                }
            }
        }

        return null;
    }

    /**
     * D&R specific book not found detection.
     */
    protected function isBookNotFound(string $html): bool
    {
        // Check parent logic first
        if (parent::isBookNotFound($html)) {
            return true;
        }

        // D&R specific checks
        $drNotFoundPatterns = [
            'ürün bulunamadı',
            'Sonuç bulunamadı',
            'Ürün bulunamadı',
            'Arama sonucu bulunamadı'
        ];

        foreach ($drNotFoundPatterns as $pattern) {
            if (stripos($html, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Extract page count from alternative HTML structure in js-detail-product div.
     * Handles format: <div class="js-detail-product">...<b>Sayfa Sayısı: </b>{{page_count}}<br>...</div>
     */
    protected function extractPageCountFromDetailDiv(\DOMXPath $xpath, array &$bookData): void
    {
        // Look for div with class js-detail-product
        $detailDivs = $xpath->query('//div[contains(@class, "js-detail-product")]');

        if ($detailDivs->length === 0) {
            return;
        }

        // Get the HTML content of the div
        $detailDiv = $detailDivs->item(0);
        $innerHTML = '';

        // Get all child nodes and reconstruct the HTML
        foreach ($detailDiv->childNodes as $child) {
            $innerHTML .= $detailDiv->ownerDocument->saveHTML($child);
        }

        // Use regex to find the page count pattern: <b>Sayfa Sayısı: </b>{{page_count}}<br>
        $pattern = '/<b>\s*Sayfa\s+Sayısı\s*:\s*<\/b>\s*(\d+)\s*<br>/i';

        if (preg_match($pattern, $innerHTML, $matches)) {
            $pageCount = (int) $matches[1];
            if ($pageCount > 0 && $pageCount <= 10000) { // Reasonable validation
                $bookData['page_count'] = $pageCount;
            }
        }
    }
}
