<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Book;
use App\Models\Activity;
use App\Models\UserActivity;


class UploadActivity extends Component
{
    use WithFileUploads;

    public $book;
    public $activity;
    public $mediaFile;
    public $description = '';
    public $isLoading = false;
    public $mode = 'create'; // 'create', 'view', 'edit'
    public $userActivity = null;
    public $existingMediaUrl = null;

    public function mount($book, $activity, $mode = 'create')
    {
        $this->book = Book::with(['authors'])->findOrFail($book);
//        $user = Auth::user();
//        $this->activity = Activity::resolvedForUser($user)->findOrFail($activity);
        $this->activity = Activity::findOrFail($activity);
        $this->mode = $mode;

        // Check if activity is media type
        if ($this->activity->activity_type !== Activity::ACTIVITY_TYPE_MEDIA) {
            abort(404);
        }

        // Get existing user activity if any
        $this->userActivity = UserActivity::where('user_id', Auth::id())
            ->where('book_id', $this->book->id)
            ->where('activity_id', $this->activity->id)
            ->first();

        // Handle different modes
        if ($this->mode === 'create' && $this->userActivity && $this->userActivity->status !== UserActivity::STATUS_REJECTED) {
            session()->flash('error', __('mobile.activity_already_completed'));
            return redirect()->route('mobile.books.activities', $this->book->id);
        }

        if (($this->mode === 'view' || $this->mode === 'edit') && !$this->userActivity) {
            session()->flash('error', __('mobile.activity_not_found'));
            return redirect()->route('mobile.books.activities', $this->book->id);
        }

        // Load existing content for view/edit modes
        if ($this->userActivity && ($this->mode === 'view' || $this->mode === 'edit')) {
            $content = json_decode($this->userActivity->content, true);
            $this->description = $content['description'] ?? '';
            $this->existingMediaUrl = $this->userActivity->media_url;
        }
    }

    public function submitActivity()
    {
        if ($this->mode === 'view') {
            return; // No submission in view mode
        }

        $validationRules = [
            'description' => 'nullable|string|max:500',
        ];

        // Only require new file if creating or editing without existing file
        if ($this->mode === 'create' || ($this->mode === 'edit' && !$this->existingMediaUrl)) {
            // Validate based on media type using resolved activity
            if ($this->activity->media_type === Activity::MEDIA_TYPE_IMAGE) {
                $validationRules['mediaFile'] = 'required|image|max:5120'; // 5MB max
            } elseif ($this->activity->media_type === Activity::MEDIA_TYPE_AUDIO) {
                $validationRules['mediaFile'] = [
                    'required',
                    'file',
                    'max:10240', // 10MB max
                    function ($attribute, $value, $fail) {
                        if ($value) {
                            $allowedMimeTypes = [
                                'audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/x-wav',
                                'audio/aac', 'audio/mp4', 'audio/x-m4a', 'audio/m4a',
                                'audio/mp4a-latm', 'audio/x-mp4', 'audio/webm', 'audio/ogg',
                                'application/octet-stream'
                            ];

                            $allowedExtensions = ['mp3', 'wav', 'm4a', 'aac', 'webm', 'ogg'];

                            $mimeType = $value->getMimeType();
                            $extension = strtolower($value->getClientOriginalExtension());

                            // Check if either MIME type or extension is allowed
                            if (!in_array($mimeType, $allowedMimeTypes) && !in_array($extension, $allowedExtensions)) {
                                $fail(__('mobile.invalid_audio_file_format'));
                            }
                        }
                    }
                ];
            } else {
                $validationRules['mediaFile'] = 'required|file|max:5120'; // Default file validation
            }
        } elseif ($this->mediaFile) {
            // Optional file validation for edit mode
            if ($this->activity->media_type === Activity::MEDIA_TYPE_IMAGE) {
                $validationRules['mediaFile'] = 'image|max:5120';
            } elseif ($this->activity->media_type === Activity::MEDIA_TYPE_AUDIO) {
                $validationRules['mediaFile'] = [
                    'file',
                    'max:10240', // 10MB max
                    function ($attribute, $value, $fail) {
                        if ($value) {
                            $allowedMimeTypes = [
                                'audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/x-wav',
                                'audio/aac', 'audio/mp4', 'audio/x-m4a', 'audio/m4a',
                                'audio/mp4a-latm', 'audio/x-mp4', 'audio/webm', 'audio/ogg',
                                'application/octet-stream'
                            ];

                            $allowedExtensions = ['mp3', 'wav', 'm4a', 'aac', 'webm', 'ogg'];

                            $mimeType = $value->getMimeType();
                            $extension = strtolower($value->getClientOriginalExtension());

                            // Check if either MIME type or extension is allowed
                            if (!in_array($mimeType, $allowedMimeTypes) && !in_array($extension, $allowedExtensions)) {
                                $fail(__('mobile.invalid_audio_file_format'));
                            }
                        }
                    }
                ];
            } else {
                $validationRules['mediaFile'] = 'file|max:5120';
            }
        }

        $this->validate($validationRules);

        $this->isLoading = true;

        try {
            // Get challenge task IDs from user_books
            $userBook = \App\Models\UserBook::where('user_id', Auth::id())
                ->where('book_id', $this->book->id)
                ->first();

            // Handle file upload
            $path = $this->existingMediaUrl; // Keep existing file by default
            if ($this->mediaFile) {
                // Store the new uploaded file
                $path = $this->mediaFile->store('user-activities', 'public');
            }

            $activityData = [
                'user_id' => Auth::id(),
                'book_id' => $this->book->id,
                'activity_id' => $this->activity->id,
                'content' => $this->description,
                'media_url' => $path,
                'activity_date' => now(),
            ];


            if ($this->mode === 'edit' && $this->userActivity) {
                // Refresh activity to ensure global scope is applied consistently
                $this->activity = Activity::findOrFail($this->activity->id);

                // Update existing activity using resolved activity settings
                if ($this->activity->need_approval) {
                    $activityData['status'] = UserActivity::STATUS_PENDING;
                }
                $this->userActivity->update($activityData);
                $userActivity = $this->userActivity->fresh();
            } else {
                // Create new activity
                $userActivity = UserActivity::create($activityData);
            }

            $successMessage = __('mobile.thank_you_for_your_submission');
            if ($this->activity->need_approval) {
                $successMessage .= ' ' . __('mobile.your_submission_is_pending_teacher_approval');
            } else {
                // Check for rewards if activity doesn't need approval (immediate completion)
                $rewardRedirect = $this->checkForRewards($userActivity);
                if ($rewardRedirect) {
                    return $rewardRedirect;
                }
            }

            session()->flash('success', $successMessage);
            return redirect()->route('mobile.books.activities', $this->book->id);
        } catch (\Exception $e) {
            Log::error(__('mobile.failed_submit_media'), [
                'user_id' => Auth::id(),
                'book_id' => $this->book->id,
                'activity_id' => $this->activity->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->isLoading = false;
            session()->flash('error', __('mobile.failed_submit_media'));
        }
    }

    /**
     * Check for newly unlocked rewards after activity completion.
     * This includes both activity-related rewards and any retroactive rewards
     * that were awarded when required activities were completed.
     */
    private function checkForRewards($userActivity)
    {
        $rewardService = app(\App\Services\MobileRewardDisplayService::class);

        // Check for all recent rewards and levels (including retroactive ones)
        // This captures both activity rewards and any reading rewards/levels
        // that were awarded retroactively when required activities were completed
        $rewardResult = $rewardService->checkForAllRecentRewards($userActivity->id);

        if ($rewardResult && $rewardResult['redirect_to_celebration']) {
            // Set redirect back to activities page after celebration
            $rewardService->setRedirectRoute('mobile.books.activities', [$this->book->id]);

            return redirect()->route('mobile.badge-unlocked');
        }

        return null;
    }

    public function render()
    {
        return view('livewire.mobile.upload-activity', [
            'activity' => $this->activity, // Pass resolved activity with class-specific settings
        ]);
    }
}
